-- Ensure UUID extension is enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the 'users' table
CREATE TABLE IF NOT EXISTS users (
	id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
	
	-- Auth fields (Step 1)
	email TEXT NOT NULL UNIQUE,
	password_hash TEXT NOT NULL,
	
	-- Profile fields (Step 2)
	first_name TEXT,
	last_name TEXT,
	first_time TEXT CHECK (first_time IN ('yes', 'no')),
	
	-- Location fields (Step 3)
	country TEXT,
	region TEXT,
	
	-- Contact fields (Step 4)
	contact_preference TEXT CHECK (contact_preference IN ('email', 'phone', 'whatsapp')),
	contact_value TEXT,
	
	-- Discovery fields (Step 5)
	how_found TEXT CHECK (how_found IN ('google', 'instagram', 'facebook', 'youtube', 'friend', 'other')),
	
	-- Knowledge fields (Step 6)
	art_knowledge TEXT CHECK (art_knowledge IN ('none', 'self', 'studied', 'degree')),
	
	-- Occupation fields (Step 7)
	occupation TEXT CHECK (occupation IN ('artist', 'not-artist', 'learn', 'study-art')),
	
	-- Marketing preferences (Step 8)
	send_marketing_info BOOLEAN DEFAULT false,
	
	-- Timestamps
	created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create the index for email lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create the timestamp update function
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
	NEW.updated_at = NOW();
	RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM pg_trigger WHERE tgname = 'set_timestamp'
	) THEN
		CREATE TRIGGER set_timestamp
		BEFORE UPDATE ON users
		FOR EACH ROW
		EXECUTE FUNCTION trigger_set_timestamp();
	END IF;
END $$;


