import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import 'boxicons/css/boxicons.min.css';
import logo from '../../assets/zonacero-logo-01.svg';
import ProfileDropdown from './ProfileDropdown';
import { useTheme } from './ThemeContext';
import {
  HeaderContainer,
  Logo,
  UserNav,
  NavItem,
  NavItemWrapper,
  Tooltip,
  BurgerMenuButton,
  MobileMenu,
  MobileMenuItem,
  MobileStudentName,
  MobileThemeToggle
} from './Header.styles';

const VideoPlayHeader = () => {
  const navigate = useNavigate();
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const closeTimeoutRef = React.useRef(null);
  const { isDarkTheme, toggleTheme } = useTheme();

  const handleHomeClick = () => {
    navigate('/dashboard');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleMobileHomeClick = () => {
    navigate('/dashboard');
    closeMobileMenu();
  };


  const handleMobileThemeToggle = () => {
    toggleTheme();
  };

  const handleMobileLogout = () => {
    console.log('Mobile logout clicked');
    // Handle logout logic here
  };

  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    setIsProfileDropdownOpen(true);
  };

  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setIsProfileDropdownOpen(false);
    }, 200);
  };

  return (
    <HeaderContainer>
      <Logo src={logo} alt="Zonacero Logo" />
      <UserNav>
        <NavItemWrapper>
          <NavItem onClick={handleHomeClick}>
            <i className='bx bx-home'></i>
          </NavItem>
          <Tooltip>Inicio</Tooltip>
        </NavItemWrapper>
        <NavItemWrapper
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <NavItem>
            <i className='bx bx-user'></i>
          </NavItem>
          <ProfileDropdown
            isOpen={isProfileDropdownOpen}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          />
        </NavItemWrapper>
      </UserNav>
      <BurgerMenuButton onClick={toggleMobileMenu}>
        <i className={isMobileMenuOpen ? 'bx bx-x' : 'bx bx-menu'}></i>
      </BurgerMenuButton>
      <MobileMenu isOpen={isMobileMenuOpen}>
        <MobileStudentName>
          Bienvenido, <span>Brayan</span>
        </MobileStudentName>
        <MobileMenuItem onClick={handleMobileHomeClick}>
          <i className='bx bx-home'></i>
          Inicio
        </MobileMenuItem>
        <MobileMenuItem onClick={handleMobileThemeToggle}>
          <i className='bx bx-moon'></i>
          Tema oscuro
          <MobileThemeToggle checked={isDarkTheme} />
        </MobileMenuItem>
        <MobileMenuItem onClick={handleMobileLogout}>
          <i className='bx bx-log-out'></i>
          Cerrar sesión
        </MobileMenuItem>
      </MobileMenu>
    </HeaderContainer>
  );
};

export default VideoPlayHeader;
