import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import logo from '../../assets/zonacero-logo-01.svg';
import {
  PageWrapper,
  LoginHeader,
  LoginContainer,
  LoginCard,
  Logo,
  Form,
  InputGroup,
  Label,
  Input,
  Button,
  LinksContainer,
  Link,
  ErrorMessage,
  BottomLegend,
} from '../Login/LoginPage.styles';
import {
  RegisterContainer,
  StepTitles,
  StepTitle,
  FormContent,
  SliderContainer,
  Slides,
  Slide,
  StepActions,
  SecondaryButton,
  Select,
  CheckboxLabel,
} from './RegisterPage.styles';

const RegisterPage = () => {
  const navigate = useNavigate();
  const { register } = useAuth();

  const [step, setStep] = useState(0);
  const steps = 8;
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const stepTitles = [
    'Cuenta',
    'Perfil',
    'Ubicación',
    'Contacto',
    'Descubrimiento',
    'Conocimientos',
    'Ocupación',
    'Confirmación'
  ];

  // Step 1 - Account
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');

  // Step 2 - Profile
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');

  // Step 3 - Location
  const [country, setCountry] = useState('');
  const [region, setRegion] = useState('');

  // Step 4 - Contact
  const [contactPreference, setContactPreference] = useState(''); // email | phone | whatsapp
  const [contactValue, setContactValue] = useState(''); // stores the phone/email value

  // Step 5 - Discovery
  const [howFound, setHowFound] = useState('');

  // Step 6 - Art knowledge
  const [knowledge, setKnowledge] = useState('');

  // Step 7 - Occupation
  const [occupation, setOccupation] = useState('');

  // Step 8 - Terms & marketing
  const [firstTime, setFirstTime] = useState(''); // yes | no (asked earlier with name)
  const [accept, setAccept] = useState(false);
  const [sendInfo, setSendInfo] = useState(true);

  function next() {
    setError('');
    // basic per-step validation
    if (step === 0) {
      if (!email.trim() || !password.trim() || !confirm.trim()) {
        setError('Completa tu correo y contraseña');
        return;
      }
      if (password !== confirm) {
        setError('Las contraseñas no coinciden');
        return;
      }
    }
    if (step === 1) {
      if (!firstName.trim() || !lastName.trim() || !firstTime) {
        setError('Completa tu nombre y si es tu primera vez');
        return;
      }
    }
    if (step === 2) {
      if (!country.trim() || !region.trim()) {
        setError('Indica tu país y estado/región');
        return;
      }
    }
    if (step === 3) {
      if (!contactPreference) {
        setError('Selecciona tu preferencia de contacto');
        return;
      }
      if (!contactValue.trim()) {
        setError('Ingresa tu información de contacto');
        return;
      }
    }
    if (step === 4) {
      if (!howFound) {
        setError('Cuéntanos cómo nos encontraste');
        return;
      }
    }
    if (step === 5) {
      if (!knowledge) {
        setError('Selecciona tus conocimientos artísticos');
        return;
      }
    }
    if (step === 6) {
      if (!occupation) {
        setError('Indica a qué te dedicas');
        return;
      }
    }
    setStep(Math.min(steps - 1, step + 1));
  }

  function back() {
    setError('');
    setStep(Math.max(0, step - 1));
  }

  async function submit(e) {
    e.preventDefault();
    setError('');
    setSuccess('');
    if (!accept) {
      setError('Debes aceptar los términos para continuar');
      return;
    }
    try {
      await register(email, password, {
        firstName,
        lastName,
        firstTime,
        country,
        region,
        contactPreference,
        contactValue,
        howFound,
        knowledge,
        occupation,
        sendInfo
      });
      setSuccess('Cuenta creada. Ahora puedes iniciar sesión.');
      setTimeout(() => navigate('/login'), 1200);
    } catch (err) {
      setError('No se pudo crear la cuenta (quizás el correo ya existe)');
    }
  }

  return (
    <PageWrapper>
      <LoginHeader>
        <Logo src={logo} alt="Zonacero Logo" />
      </LoginHeader>
      <LoginContainer>
        <LoginCard $wide>
          <ErrorMessage isVisible={!!(error || success)}>{error || success}</ErrorMessage>

          <RegisterContainer>
            <StepTitles>
              {stepTitles.map((title, index) => (
                <StepTitle key={index} $active={step === index}>
                  {index + 1}. {title}
                </StepTitle>
              ))}
            </StepTitles>

            <FormContent>
              <Form onSubmit={submit}>
            <SliderContainer>
              <Slides $step={step} $steps={steps}>
                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="email">Correo electrónico</Label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      autoComplete="email"
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <Label htmlFor="password">Contraseña</Label>
                    <Input
                      type="password"
                      id="password"
                      name="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="••••••••"
                      autoComplete="new-password"
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <Label htmlFor="confirm">Confirmar contraseña</Label>
                    <Input
                      type="password"
                      id="confirm"
                      name="confirm"
                      value={confirm}
                      onChange={(e) => setConfirm(e.target.value)}
                      placeholder="••••••••"
                      autoComplete="new-password"
                      required
                    />
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="firstName">Nombre</Label>
                    <Input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      placeholder="Tu nombre"
                      autoComplete="given-name"
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <Label htmlFor="lastName">Apellido</Label>
                    <Input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      placeholder="Tus apellidos"
                      autoComplete="family-name"
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <Label htmlFor="firstTime">¿Es tu primera vez?</Label>
                    <Select
                      id="firstTime"
                      name="firstTime"
                      value={firstTime}
                      onChange={(e) => setFirstTime(e.target.value)}
                      required
                    >
                      <option value="" disabled>Selecciona una opción</option>
                      <option value="yes">Sí</option>
                      <option value="no">No</option>
                    </Select>
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="country">País</Label>
                    <Input
                      type="text"
                      id="country"
                      name="country"
                      value={country}
                      onChange={(e) => setCountry(e.target.value)}
                      placeholder="México"
                      autoComplete="country-name"
                      required
                    />
                  </InputGroup>
                  <InputGroup>
                    <Label htmlFor="region">Estado / Región</Label>
                    <Input
                      type="text"
                      id="region"
                      name="region"
                      value={region}
                      onChange={(e) => setRegion(e.target.value)}
                      placeholder="Jalisco"
                      autoComplete="address-level1"
                      required
                    />
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="contactPreference">Preferencia de contacto</Label>
                    <Select
                      id="contactPreference"
                      name="contactPreference"
                      value={contactPreference}
                      onChange={(e) => {
                        setContactPreference(e.target.value);
                        setContactValue('');
                      }}
                      required
                    >
                      <option value="" disabled>Selecciona una opción</option>
                      <option value="email">Correo electrónico</option>
                      <option value="phone">Teléfono</option>
                      <option value="whatsapp">WhatsApp</option>
                    </Select>
                  </InputGroup>

                  {contactPreference && (
                    <InputGroup>
                      <Label htmlFor="contactValue">
                        {contactPreference === 'email' ? 'Tu correo electrónico' : 'Tu número de teléfono'}
                      </Label>
                      <Input
                        type={contactPreference === 'email' ? 'email' : 'tel'}
                        id="contactValue"
                        name="contactValue"
                        value={contactValue}
                        onChange={(e) => setContactValue(e.target.value)}
                        placeholder={contactPreference === 'email' ? '<EMAIL>' : '10 dígitos'}
                        autoComplete={contactPreference === 'email' ? 'email' : 'tel'}
                        required
                      />
                    </InputGroup>
                  )}
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="howFound">¿Cómo nos encontraste?</Label>
                    <Select
                      id="howFound"
                      name="howFound"
                      value={howFound}
                      onChange={(e) => setHowFound(e.target.value)}
                      required
                    >
                      <option value="" disabled>Selecciona una opción</option>
                      <option value="google">Google</option>
                      <option value="instagram">Instagram</option>
                      <option value="facebook">Facebook</option>
                      <option value="youtube">YouTube</option>
                      <option value="friend">Recomendación</option>
                      <option value="other">Otro</option>
                    </Select>
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="knowledge">Conocimientos artísticos</Label>
                    <Select
                      id="knowledge"
                      name="knowledge"
                      value={knowledge}
                      onChange={(e) => setKnowledge(e.target.value)}
                      required
                    >
                      <option value="" disabled>Selecciona una opción</option>
                      <option value="none">Ninguno</option>
                      <option value="self">Autodidacta</option>
                      <option value="studied">Estudié cursos/talleres</option>
                      <option value="degree">Estudio/estudié a nivel licenciatura</option>
                    </Select>
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <InputGroup>
                    <Label htmlFor="occupation">¿A qué te dedicas?</Label>
                    <Select
                      id="occupation"
                      name="occupation"
                      value={occupation}
                      onChange={(e) => setOccupation(e.target.value)}
                      required
                    >
                      <option value="" disabled>Selecciona una opción</option>
                      <option value="artist">Soy artista plástico</option>
                      <option value="not-artist">No soy artista</option>
                      <option value="learn">Solo me interesa aprender</option>
                      <option value="study-art">Estudio arte</option>
                    </Select>
                  </InputGroup>
                </Slide>

                <Slide $steps={steps}>
                  <CheckboxLabel>
                    <input
                      type="checkbox"
                      checked={accept}
                      onChange={(e) => setAccept(e.target.checked)}
                    />
                    <span>Acepto los términos y condiciones</span>
                  </CheckboxLabel>
                  <CheckboxLabel>
                    <input
                      type="checkbox"
                      checked={sendInfo}
                      onChange={(e) => setSendInfo(e.target.checked)}
                    />
                    <span>¿Quieres que te enviemos información?</span>
                  </CheckboxLabel>
                </Slide>
              </Slides>
            </SliderContainer>

            <StepActions>
              {step > 0 && (
                <SecondaryButton type="button" onClick={back}>Atrás</SecondaryButton>
              )}
              {step < steps - 1 && (
                <Button type="button" onClick={next}>Siguiente</Button>
              )}
              {step === steps - 1 && (
                <Button type="submit">Crear cuenta</Button>
              )}
            </StepActions>
              </Form>
            </FormContent>
          </RegisterContainer>

          <LinksContainer>
            <Link href="#" onClick={(e) => { e.preventDefault(); navigate('/login'); }}>
              ¿Ya tienes cuenta? Inicia sesión
            </Link>
          </LinksContainer>
        </LoginCard>
      </LoginContainer>
      <BottomLegend>Powered by Aletheia Solutions</BottomLegend>
    </PageWrapper>
  );
};

export default RegisterPage;


