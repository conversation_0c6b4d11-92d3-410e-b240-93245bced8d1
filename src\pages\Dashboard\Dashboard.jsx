import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';
import {
  DashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  CourseFilter,
  MainContent,
  CourseItem,
  ImgFrame,
  VideoCountBadge,
  CourseInfo,
  CourseName,
  PlaylistMetadata,
  LoadingText,
  ErrorText,
  NoResultsText
} from './Dashboard.styles';

const Dashboard = () => {
  const navigate = useNavigate();
  const [playlists, setPlaylists] = useState([]);
  const [loading, setLoading] = useState(true); // Set to true to load real data
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/my/courses');
      // Transform API response to match expected format
      const transformedCourses = response.data.map(course => ({
        id: course.id,
        name: course.title,
        thumbnail: course.youtube_thumbnail_url || 'https://via.placeholder.com/320x180/FF6B6B/FFFFFF?text=Course',
        videoCount: course.youtube_video_count || 0,
        metadata: `Actualizado: ${new Date(course.granted_at).toLocaleDateString()}`,
        description: course.description || 'Descripción del curso'
      }));
      setPlaylists(transformedCourses);
      setError(null);
    } catch (err) {
      console.error('Error loading courses:', err);
      setError('Error cargando cursos');
      setPlaylists([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search functionality
  const handleSearch = async (query) => {
    if (!query.trim()) {
      setError(null);
      return;
    }

    try {
      setLoading(true);
      // Filter current courses/playlists
      const filteredResults = playlists.filter(course =>
        course.name?.toLowerCase().includes(query.toLowerCase())
      );
      setPlaylists(filteredResults);
      setError(null);
    } catch (err) {
      console.error('Search failed:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Use loaded courses data
  const displayCourses = playlists;

  return (
    <DashboardContainer>
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Mis Cursos</MainTitle>
          <CourseFilter
            name="course-filter"
            id="course-filter"
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
          >
            <option value="all">Todos los cursos</option>
            <option value="playlist">Pintura</option>
            <option value="course">Dibujo</option>
          </CourseFilter>
        </MainHeader>
        <MainContent>
          {loading && <LoadingText>Cargando playlists...</LoadingText>}
          {error && <ErrorText>Error: {error}</ErrorText>}
          {!loading && !error && displayCourses.length === 0 && (
            <NoResultsText>No se encontraron playlists</NoResultsText>
          )}
          {!loading && !error && displayCourses.map((course, index) => (
            <CourseItem
              key={course.id || index}
              onClick={() => navigate(`/course-video/${course.id || index}`)}
            >
              <ImgFrame>
                {course.thumbnail && (
                  <img src={course.thumbnail} alt={course.name} />
                )}
                {course.videoCount && (
                  <VideoCountBadge>{course.videoCount} videos</VideoCountBadge>
                )}
              </ImgFrame>
              <CourseInfo>
                <CourseName>{course.name}</CourseName>
                <PlaylistMetadata>{course.metadata}</PlaylistMetadata>
              </CourseInfo>
            </CourseItem>
          ))}
        </MainContent>
      </Main>
      <Footer />
    </DashboardContainer>
  );
};

export default Dashboard;
