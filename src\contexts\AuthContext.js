import React, { createContext, useContext, useEffect, useState, useMemo } from 'react';
import api from '../services/api';

const AuthContext = createContext(null);

export function AuthProvider({ children }) {
	const [user, setUser] = useState(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		let isMounted = true;
		async function loadMe() {
			try {
				const res = await api.get('/api/auth/me');
				if (!isMounted) return;
				setUser(res.data);
			} catch (err) {
				if (!isMounted) return;
				setUser(null);
			} finally {
				if (isMounted) setIsLoading(false);
			}
		}
		loadMe();
		return () => {
			isMounted = false;
		};
	}, []);

	async function login(email, password) {
		const res = await api.post('/api/auth/login', { email, password });
		setUser(res.data);
		return res.data;
	}

	async function logout() {
		await api.post('/api/auth/logout');
		setUser(null);
	}

	async function register(email, password, userData) {
		return api.post('/api/auth/register', { email, password, ...userData });
	}

	const value = useMemo(
		() => ({ 
			user, 
			isLoading, 
			login, 
			logout, 
			register,
			isAdmin: user?.role === 'admin'
		}),
		[user, isLoading]
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
	const ctx = useContext(AuthContext);
	if (!ctx) throw new Error('useAuth must be used within AuthProvider');
	return ctx;
}


