import React, { useState, useEffect } from 'react';
import AdminDashboardHeader from '../../components/Layout/AdminDashboardHeader';
import Footer from '../../components/Layout/Footer';
import api from '../../services/api';
import {
  AdminDashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  MainContent,
  StudentList,
  StudentItem,
  StudentInfo,
  StudentName,
  StudentEmail,
  StudentActions,
  ActionButton,
  LoadingText,
  ErrorText,
  EmptyText,
  AddStudentButton,
  Modal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalBody,
  FormGroup,
  Label,
  Input,
  Textarea,
  ModalFooter,
  CancelButton,
  SubmitButton
} from './AdminDashboard.styles';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('students');
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showCourseModal, setShowCourseModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showAddCourseModal, setShowAddCourseModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [studentCourses, setStudentCourses] = useState([]);
  const [message, setMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [newStudent, setNewStudent] = useState({ email: '', password: '', firstName: '', lastName: '' });
  const [newCourse, setNewCourse] = useState({
    title: '',
    description: '',
    playlistUrl: ''
  });
  const [addingCourse, setAddingCourse] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Filter students based on search query
    if (!searchQuery.trim()) {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(student =>
        `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [students, searchQuery]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsResponse, coursesResponse] = await Promise.all([
        api.get('/api/admin/users'),
        api.get('/api/admin/courses')
      ]);

      // Filter out admin users to show only students
      const studentUsers = studentsResponse.data.filter(user => user.role !== 'admin');
      setStudents(studentUsers);
      setFilteredStudents(studentUsers); // Initially show all students
      setCourses(coursesResponse.data);
      setError(null);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Error cargando datos');
    } finally {
      setLoading(false);
    }
  };

  const loadStudentCourses = async (studentId) => {
    try {
      const response = await api.get(`/api/admin/users/${studentId}/courses`);
      setStudentCourses(response.data || []);
    } catch (err) {
      console.error('Error loading student courses:', err);
      setStudentCourses([]);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleAddStudent = async (e) => {
    e.preventDefault();
    try {
      await api.post('/api/admin/create-user', newStudent);
      setShowAddModal(false);
      setNewStudent({ email: '', password: '', firstName: '', lastName: '' });
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error adding student:', err);
      setError('Error agregando estudiante');
    }
  };

  const handleRemoveStudent = async (studentId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este estudiante?')) {
      return;
    }
    try {
      await api.delete(`/api/admin/users/${studentId}`);
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error removing student:', err);
      setError('Error eliminando estudiante');
    }
  };

  const handleManageCourses = async (student) => {
    setSelectedStudent(student);
    await loadStudentCourses(student.id);
    setShowCourseModal(true);
  };

  const handleToggleCourseAccess = async (courseId, hasAccess) => {
    if (!selectedStudent) return;

    try {
      if (hasAccess) {
        // Revoke access
        await api.delete(`/api/admin/users/${selectedStudent.id}/courses/${courseId}`);
      } else {
        // Grant access
        await api.post(`/api/admin/users/${selectedStudent.id}/courses/${courseId}`);
      }
      // Reload student's courses
      await loadStudentCourses(selectedStudent.id);
    } catch (err) {
      console.error('Error toggling course access:', err);
      setError('Error cambiando acceso al curso');
    }
  };

  const handleSendMessage = async (student) => {
    setSelectedStudent(student);
    setMessage('');
    setShowMessageModal(true);
  };

  const handleSendMessageSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    try {
      // TODO: Implement backend messaging endpoint
      alert(`Mensaje enviado a ${selectedStudent.email}: ${message}`);
      setShowMessageModal(false);
      setMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Error enviando mensaje');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDeleteCourse = async (courseId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este curso?')) {
      return;
    }
    try {
      await api.delete(`/api/admin/courses/${courseId}`);
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error deleting course:', err);
      setError('Error eliminando curso');
    }
  };

  const handleAddCourse = async (e) => {
    e.preventDefault();
    if (!newCourse.title.trim() || !newCourse.playlistUrl.trim()) {
      setError('El título y la URL de la playlist son requeridos');
      return;
    }
    
    setAddingCourse(true);
    try {
      await api.post('/api/admin/courses', {
        title: newCourse.title,
        description: newCourse.description,
        playlistUrl: newCourse.playlistUrl
      });
      setShowAddCourseModal(false);
      setNewCourse({ title: '', description: '', playlistUrl: '' });
      loadData(); // Reload the list
      setError(null);
    } catch (err) {
      console.error('Error adding course:', err);
      setError(err.response?.data?.error || 'Error agregando curso');
    } finally {
      setAddingCourse(false);
    }
  };

  const handleCourseInputChange = (e) => {
    const { name, value } = e.target;
    setNewCourse(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <AdminDashboardContainer>
      <AdminDashboardHeader
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Panel de Administración</MainTitle>
          <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
            <button
              onClick={() => setActiveTab('students')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'students' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'students' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Estudiantes
            </button>
            <button
              onClick={() => setActiveTab('courses')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'courses' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'courses' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Cursos
            </button>
          </div>
          {activeTab === 'students' && (
            <AddStudentButton onClick={() => setShowAddModal(true)}>
              Agregar Estudiante
            </AddStudentButton>
          )}
        </MainHeader>
        <MainContent>
          {activeTab === 'students' && (
            <>
              {loading && <LoadingText>Cargando estudiantes...</LoadingText>}
              {error && <ErrorText>Error: {error}</ErrorText>}
              {!loading && !error && students.length === 0 && (
                <EmptyText>No hay estudiantes registrados</EmptyText>
              )}
              {!loading && !error && students.length > 0 && filteredStudents.length === 0 && (
                <EmptyText>No se encontraron estudiantes con esa búsqueda</EmptyText>
              )}
              {!loading && !error && filteredStudents.length > 0 && (
                <StudentList>
                  {filteredStudents.map((student) => (
                    <StudentItem key={student.id}>
                      <StudentInfo>
                        <StudentName>
                          {student.first_name && student.last_name
                            ? `${student.first_name} ${student.last_name}`
                            : 'Sin nombre'}
                        </StudentName>
                        <StudentEmail>{student.email}</StudentEmail>
                      </StudentInfo>
                      <StudentActions>
                        <ActionButton onClick={() => handleManageCourses(student)}>
                          Gestionar Cursos
                        </ActionButton>
                        <ActionButton onClick={() => handleSendMessage(student)}>
                          Enviar Mensaje
                        </ActionButton>
                        <ActionButton
                          onClick={() => handleRemoveStudent(student.id)}
                          danger
                        >
                          Eliminar
                        </ActionButton>
                      </StudentActions>
                    </StudentItem>
                  ))}
                </StudentList>
              )}
            </>
          )}

          {activeTab === 'courses' && (
            <>
              {loading && <LoadingText>Cargando cursos...</LoadingText>}
              {error && <ErrorText>Error: {error}</ErrorText>}
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <h3 style={{ margin: 0 }}>Gestionar Cursos</h3>
                <button
                  onClick={() => setShowAddCourseModal(true)}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: 'var(--color-primary)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Agregar Curso
                </button>
              </div>
              {!loading && !error && courses.length === 0 && (
                <EmptyText>No hay cursos registrados</EmptyText>
              )}
              {!loading && !error && courses.length > 0 && (
                <div style={{ display: 'grid', gap: '1rem' }}>
                  {courses.map((course) => (
                    <div
                      key={course.id}
                      style={{
                        padding: '1rem',
                        border: '1px solid var(--color-border)',
                        borderRadius: '4px',
                        backgroundColor: 'var(--color-background-secondary)',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <div>
                        <h4 style={{ margin: '0 0 0.5rem 0' }}>{course.title}</h4>
                        {course.description && (
                          <p style={{ margin: 0, fontSize: '0.9rem', color: 'var(--color-text-secondary)' }}>
                            {course.description}
                          </p>
                        )}
                        {course.youtube_playlist_url && (
                          <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.8rem' }}>
                            <a href={course.youtube_playlist_url} target="_blank" rel="noopener noreferrer" style={{ color: 'var(--color-primary)', textDecoration: 'none' }}>
                              Ver Playlist de YouTube ↗
                            </a>
                          </p>
                        )}
                        <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.8rem', color: 'var(--color-text-secondary)' }}>
                          ID: {course.id}
                        </p>
                      </div>
                      <button
                        onClick={() => handleDeleteCourse(course.id)}
                        style={{
                          padding: '0.5rem 1rem',
                          backgroundColor: '#ff6b6b',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                      >
                        Eliminar
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </MainContent>
      </Main>
      <Footer />

      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Agregar Nuevo Estudiante</ModalTitle>
            </ModalHeader>
            <form onSubmit={handleAddStudent}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={newStudent.firstName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={newStudent.lastName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={newStudent.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="password">Contraseña</Label>
                  <Input
                    type="password"
                    id="password"
                    name="password"
                    value={newStudent.password}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowAddModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Agregar Estudiante
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}

      {showCourseModal && selectedStudent && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Gestionar Cursos - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <ModalBody>
              {courses.length === 0 ? (
                <p>No hay cursos disponibles</p>
              ) : (
                courses.map((course) => {
                  const hasAccess = studentCourses.some(sc => sc.id === course.id);
                  return (
                    <FormGroup key={course.id}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <strong>{course.title}</strong>
                          {course.description && <p style={{ margin: '4px 0', fontSize: 'var(--font-size-sm)', opacity: 0.8 }}>{course.description}</p>}
                        </div>
                        <ActionButton
                          onClick={() => handleToggleCourseAccess(course.id, hasAccess)}
                          style={{
                            backgroundColor: hasAccess ? '#ff6b6b' : 'var(--color-primary)',
                            color: 'white',
                            minWidth: '120px'
                          }}
                        >
                          {hasAccess ? 'Revocar' : 'Otorgar'}
                        </ActionButton>
                      </div>
                    </FormGroup>
                  );
                })
              )}
            </ModalBody>
            <ModalFooter>
              <CancelButton onClick={() => setShowCourseModal(false)}>
                Cerrar
              </CancelButton>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {showMessageModal && selectedStudent && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Enviar Mensaje - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <form onSubmit={handleSendMessageSubmit}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="message">Mensaje</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Escribe tu mensaje aquí..."
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowMessageModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Enviar Mensaje
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}

      {showAddCourseModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Agregar Nuevo Curso</ModalTitle>
            </ModalHeader>
            <form onSubmit={handleAddCourse}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="courseTitle">Título del Curso</Label>
                  <Input
                    type="text"
                    id="courseTitle"
                    name="title"
                    value={newCourse.title}
                    onChange={handleCourseInputChange}
                    placeholder="Ej: Introducción a la Pintura"
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="courseDescription">Descripción (opcional)</Label>
                  <Textarea
                    id="courseDescription"
                    name="description"
                    value={newCourse.description}
                    onChange={handleCourseInputChange}
                    placeholder="Descripción del curso..."
                    rows={3}
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="playlistUrl">URL de Playlist de YouTube</Label>
                  <Input
                    type="url"
                    id="playlistUrl"
                    name="playlistUrl"
                    value={newCourse.playlistUrl}
                    onChange={handleCourseInputChange}
                    placeholder="https://www.youtube.com/playlist?list=..."
                    required
                  />
                  <p style={{ fontSize: '0.8rem', color: 'var(--color-text-secondary)', marginTop: '0.25rem' }}>
                    Copia y pega el enlace completo de la playlist de YouTube
                  </p>
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowAddCourseModal(false)} disabled={addingCourse}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit" disabled={addingCourse}>
                  {addingCourse ? 'Agregando...' : 'Agregar Curso'}
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}
    </AdminDashboardContainer>
  );
};

export default AdminDashboard;
