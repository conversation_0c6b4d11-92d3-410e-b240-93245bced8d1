import styled from 'styled-components';

export const DashboardContainer = styled.div`
  height: 100dvh;
  max-width: var(--max-width);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
`;


export const Main = styled.main`
  height: calc(100dvh - var(--header-height) - var(--footer-height));
  width: 100%;
  margin-top: var(--header-height);
  display: flex;
  flex-direction: column;
`;

export const MainHeader = styled.div`
  width: 100%;
  height: 50px;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: var(--border-width) var(--border-style) var(--border-color);
  margin-bottom: var(--spacing-lg);
`;

export const MainTitle = styled.h1`
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  user-select: none;
`;

export const CourseFilter = styled.select`
  min-width: 150px;
  height: 90%;
  padding: 0 var(--spacing-lg);
  border: none;
  border-radius: 12px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  background-color: var(--color-background);
  transition: background-color 0.3s ease;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--color-background-soft);
  }

  &:focus {
    outline: none;
}
`;

export const MainContent = styled.div`
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  align-items: start;
  scrollbar-width: none;
  pointer-events: auto;
  data-scrollable: true;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-hover);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
  }

  /* Responsive grid */
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

export const ImgFrame = styled.div`
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: var(--color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-bottom: var(--border-width) var(--border-style) var(--border-color);
  overflow: hidden;

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }
`;

// Removed multi-layer image components (BackImage, MiddleImage, FrontImage) in favor of a single image inside ImgFrame

export const CourseItem = styled.div`
  border-radius: 16px;
  overflow: hidden;
  transition: border 0.2s ease;
  border: 3px solid var(--border-color);
  display: flex;
  flex-direction: column;
  min-height: 380px;
  height: auto;
  cursor: pointer;
  pointer-events: auto;
  user-select: none;

  &:hover {
    border: 3px solid var(--color-primary);
  }
`;

export const VideoCountBadge = styled.div`
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--color-primary);
  color: var(--color-background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 12px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  user-select: none;
`;

export const CourseInfo = styled.div`
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--spacing-sm);
  min-height: 120px;
`;

export const CourseName = styled.h2`
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;
`;

export const PlaylistMetadata = styled.p`
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  opacity: 0.8;
  flex-shrink: 0;
  user-select: none;
`;

// Text content components for better contrast
export const LoadingText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;

export const ErrorText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;

export const NoResultsText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;
