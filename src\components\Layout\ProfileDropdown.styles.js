import styled from 'styled-components';

export const DropdownContainer = styled.div`
  position: ${props => props.$mobile ? 'static' : 'absolute'};
  top: ${props => props.$mobile ? 'auto' : 'calc(100% - 5px)'};
  right: ${props => props.$mobile ? 'auto' : '0'};
  z-index: 1002;
  min-width: ${props => props.$mobile ? '100%' : '200px'};
  padding-top: ${props => props.$mobile ? '0' : '13px'};
  pointer-events: auto;
  margin-top: ${props => props.$mobile ? 'var(--spacing-sm)' : '0'};
`;

export const DropdownMenu = styled.div`
  background-color: var(--color-background);
  border: var(--border-width) var(--border-style) var(--color-border);
  border-radius: 8px;
  box-shadow: ${props => props.$mobile ? 'none' : '0 4px 12px rgba(0, 0, 0, 0.15)'};
  overflow: hidden;
  position: relative;
  z-index: 1003;
  pointer-events: auto;
  transition: all 0.3s ease;
`;

export const DropdownItem = styled.div`
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;
  cursor: pointer;
  user-select: none;
  min-height: ${props => props.$mobile ? '44px' : 'auto'};

  &:hover {
    background-color: var(--color-background-soft);
  }

  &:active {
    background-color: ${props => props.$mobile ? 'var(--color-hover)' : 'var(--color-background-soft)'};
  }
`;

export const ThemeToggleContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--spacing-md);
`;

export const ThemeToggleLabel = styled.span`
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  flex: 1;
  user-select: none;
`;

export const ThemeToggleSwitch = styled.input`
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
`;

export const ThemeToggleSlider = styled.span`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  background-color: ${props => props.checked ? 'var(--color-primary)' : '#ccc'};
  border-radius: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  user-select: none;

  &::before {
    content: '';
    position: absolute;
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    transform: ${props => props.checked ? 'translateX(20px)' : 'translateX(0)'};
  }
`;

export const LogoutItem = styled.div`
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
  border-top: 1px solid var(--color-border);
  user-select: none;
  min-height: ${props => props.$mobile ? '44px' : 'auto'};
  display: ${props => props.$mobile ? 'flex' : 'block'};
  align-items: ${props => props.$mobile ? 'center' : 'initial'};

  &:hover {
    background-color: var(--color-background-soft);
    color: var(--color-primary);
  }

  &:active {
    transform: ${props => props.$mobile ? 'none' : 'scale(0.98)'};
    background-color: ${props => props.$mobile ? 'var(--color-error)' : 'var(--color-background-soft)'};
    color: ${props => props.$mobile ? 'var(--color-background)' : 'var(--color-primary)'};
  }
`;
