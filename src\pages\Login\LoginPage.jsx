import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import logo from '../../assets/zonacero-logo-01.svg';
import {
  PageWrapper,
  LoginHeader,
  LoginContainer,
  LoginCard,
  Logo,
  Form,
  InputGroup,
  Label,
  Input,
  Button,
  LinksContainer,
  Link,
  ErrorMessage,
  BottomLegend
} from './LoginPage.styles';

const LoginPage = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    if (error) setError('');
  };

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) {
      setError('Por favor ingresa correo y contraseña');
      return;
    }
    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (err) {
      setError('Credenciales inválidas o error de servidor');
    }
  };

  return (
    <PageWrapper>
      <LoginHeader>
        <Logo src={logo} alt="Zonacero Logo" />
      </LoginHeader>
      <LoginContainer>
        <LoginCard>
          <ErrorMessage isVisible={!!error}>{error}</ErrorMessage>

          <Form onSubmit={handleSubmit}>
            <InputGroup>
              <Label htmlFor="email">Correo electrónico</Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={email}
                onChange={handleEmailChange}
                placeholder="<EMAIL>"
                autoComplete="email"
                required
              />
            </InputGroup>

            <InputGroup>
              <Label htmlFor="password">Contraseña</Label>
              <Input
                type="password"
                id="password"
                name="password"
                value={password}
                onChange={handlePasswordChange}
                placeholder="••••••••"
                autoComplete="current-password"
                required
              />
            </InputGroup>

            <Button type="submit">
              Iniciar Sesión
            </Button>
          </Form>

        <LinksContainer>
          <Link href="#" onClick={(e) => { e.preventDefault(); navigate('/register'); }}>
            Crear cuenta
          </Link>
          <Link href="#" onClick={(e) => { e.preventDefault(); navigate('/forgot-password'); }}>
            ¿Olvidaste tu contraseña?
          </Link>
        </LinksContainer>
        </LoginCard>
      </LoginContainer>
      <BottomLegend>Powered by Aletheia Solutions</BottomLegend>
    </PageWrapper>
  );
};

export default LoginPage;
