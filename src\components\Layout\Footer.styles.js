import styled from 'styled-components';

export const FooterContainer = styled.footer`
  height: var(--footer-height);
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: var(--border-width) var(--border-style) var(--border-color);
  background-color: var(--color-background);
`;

export const FooterText = styled.p`
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: 0;
  color: var(--color-text);
  user-select: none;
`;

export const FooterLink = styled.a`
  color: var(--color-text);
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: var(--color-primary);
  }
`;