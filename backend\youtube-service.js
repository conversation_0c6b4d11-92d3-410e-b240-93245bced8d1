const { google } = require('googleapis');
const db = require('./db');

class YouTubeService {
    constructor() {
        this.youtube = google.youtube('v3');
    }

    /**
     * Get authenticated YouTube client for a user
     */
    async getAuthenticatedClient(userId) {
        try {
            const result = await db.query(
                `SELECT youtube_api_key, youtube_client_id, youtube_client_secret,
                        youtube_refresh_token, youtube_access_token, youtube_token_expiry
                 FROM users WHERE id = $1`,
                [userId]
            );

            if (result.rowCount === 0) {
                throw new Error('User not found');
            }

            const user = result.rows[0];
            const { youtube_api_key, youtube_client_id, youtube_client_secret,
                    youtube_refresh_token, youtube_access_token, youtube_token_expiry } = user;

            if (!youtube_api_key) {
                throw new Error('YouTube API key not configured');
            }

            // For basic operations (public playlists), we can use the API key
            const client = google.youtube({
                version: 'v3',
                auth: youtube_api_key
            });

            // For private/unlisted content, we need OAuth2
            if (youtube_client_id && youtube_client_secret && youtube_refresh_token) {
                const oauth2Client = new google.auth.OAuth2(
                    youtube_client_id,
                    youtube_client_secret
                );

                oauth2Client.setCredentials({
                    refresh_token: youtube_refresh_token,
                    access_token: youtube_access_token,
                    expiry_date: youtube_token_expiry ? new Date(youtube_token_expiry).getTime() : null
                });

                // Refresh token if expired
                if (!youtube_access_token || (youtube_token_expiry && new Date(youtube_token_expiry) <= new Date())) {
                    const tokens = await oauth2Client.refreshAccessToken();
                    const newCredentials = tokens.credentials;

                    // Update database with new tokens
                    await db.query(
                        `UPDATE users SET
                            youtube_access_token = $1,
                            youtube_refresh_token = $2,
                            youtube_token_expiry = $3
                         WHERE id = $4`,
                        [
                            newCredentials.access_token,
                            newCredentials.refresh_token,
                            new Date(newCredentials.expiry_date),
                            userId
                        ]
                    );

                    oauth2Client.setCredentials(newCredentials);
                }

                return google.youtube({
                    version: 'v3',
                    auth: oauth2Client
                });
            }

            return client;
        } catch (error) {
            console.error('Error getting authenticated YouTube client:', error);
            throw error;
        }
    }

    /**
     * Get user's YouTube channel information
     */
    async getChannelInfo(userId) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            // First get user's channel ID if not stored
            let channelId;
            const userResult = await db.query('SELECT youtube_channel_id FROM users WHERE id = $1', [userId]);

            if (userResult.rows[0].youtube_channel_id) {
                channelId = userResult.rows[0].youtube_channel_id;
            } else {
                // Get channel info from authenticated user
                const channelsResponse = await client.channels.list({
                    part: 'id,snippet',
                    mine: true
                });

                if (channelsResponse.data.items && channelsResponse.data.items.length > 0) {
                    channelId = channelsResponse.data.items[0].id;

                    // Store channel ID in database
                    await db.query('UPDATE users SET youtube_channel_id = $1 WHERE id = $2', [channelId, userId]);
                }
            }

            if (!channelId) {
                throw new Error('Could not determine YouTube channel ID');
            }

            // Get detailed channel information
            const channelResponse = await client.channels.list({
                part: 'snippet,statistics',
                id: channelId
            });

            return channelResponse.data.items[0];
        } catch (error) {
            console.error('Error getting channel info:', error);
            throw error;
        }
    }

    /**
     * Get playlists from user's channel
     */
    async getChannelPlaylists(userId) {
        try {
            const client = await this.getAuthenticatedClient(userId);
            const userResult = await db.query('SELECT youtube_channel_id FROM users WHERE id = $1', [userId]);

            if (!userResult.rows[0].youtube_channel_id) {
                throw new Error('YouTube channel ID not configured');
            }

            const channelId = userResult.rows[0].youtube_channel_id;

            const response = await client.playlists.list({
                part: 'snippet,status,contentDetails',
                channelId: channelId,
                maxResults: 50
            });

            return response.data.items || [];
        } catch (error) {
            console.error('Error getting channel playlists:', error);
            throw error;
        }
    }

    /**
     * Get videos from a specific playlist
     */
    async getPlaylistVideos(userId, playlistId) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            const response = await client.playlistItems.list({
                part: 'snippet,contentDetails,status',
                playlistId: playlistId,
                maxResults: 50
            });

            return response.data.items || [];
        } catch (error) {
            console.error('Error getting playlist videos:', error);
            throw error;
        }
    }





    /**
     * Sync a YouTube playlist as a course
     */
    async syncPlaylistAsCourse(userId, playlistId, customTitle = null, customDescription = null) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            // Get playlist details
            const playlistResponse = await client.playlists.list({
                part: 'snippet,status,contentDetails',
                id: playlistId
            });

            if (!playlistResponse.data.items || playlistResponse.data.items.length === 0) {
                throw new Error('Playlist not found');
            }

            const playlist = playlistResponse.data.items[0];
            const videoCount = playlist.contentDetails ? playlist.contentDetails.itemCount : 0;

            // Create or update course
            const courseData = {
                youtube_playlist_id: playlistId,
                youtube_playlist_url: `https://www.youtube.com/playlist?list=${playlistId}`,
                youtube_channel_title: playlist.snippet.channelTitle,
                youtube_video_count: videoCount,
                youtube_thumbnail_url: playlist.snippet.thumbnails?.default?.url,
                synced_at: new Date(),
                title: customTitle || playlist.snippet.title,
                description: customDescription || playlist.snippet.description,
                slug: this.generateSlug(customTitle || playlist.snippet.title)
            };

            // Check if course already exists
            const existingCourse = await db.query(
                'SELECT id FROM courses WHERE youtube_playlist_id = $1',
                [playlistId]
            );

            let courseId;
            if (existingCourse.rowCount > 0) {
                // Update existing course
                courseId = existingCourse.rows[0].id;
                await db.query(
                    `UPDATE courses SET
                        title = $1,
                        description = $2,
                        youtube_channel_title = $3,
                        youtube_video_count = $4,
                        youtube_thumbnail_url = $5,
                        synced_at = $6,
                        updated_at = NOW()
                     WHERE id = $7`,
                    [
                        courseData.title,
                        courseData.description,
                        courseData.youtube_channel_title,
                        courseData.youtube_video_count,
                        courseData.youtube_thumbnail_url,
                        courseData.synced_at,
                        courseId
                    ]
                );
            } else {
                // Create new course
                const result = await db.query(
                    `INSERT INTO courses (
                        slug, title, description, youtube_playlist_id, youtube_playlist_url,
                        youtube_channel_title, youtube_video_count, youtube_thumbnail_url, synced_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`,
                    [
                        courseData.slug,
                        courseData.title,
                        courseData.description,
                        courseData.youtube_playlist_id,
                        courseData.youtube_playlist_url,
                        courseData.youtube_channel_title,
                        courseData.youtube_video_count,
                        courseData.youtube_thumbnail_url,
                        courseData.synced_at
                    ]
                );
                courseId = result.rows[0].id;
            }

            return { courseId, ...courseData };
        } catch (error) {
            console.error('Error syncing playlist as course:', error);
            throw error;
        }
    }

    /**
     * Generate URL-friendly slug from title
     */
    generateSlug(title) {
        return title
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    }
}

module.exports = new YouTubeService();
