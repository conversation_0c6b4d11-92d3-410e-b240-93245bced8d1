import { css } from 'styled-components';

// CSS Custom Properties for Theme
export const theme = css`
  :root {
    /* Color Palette */
    --color-primary: #FE650D; /* Orange for primary accents */
    --color-text: #000000; /* Black for all text */
    --color-border: #000000; /* Black for borders */
    --color-background: whitesmoke; /* Whitesmoke for body and header background */
    --color-background-soft:rgb(230, 230, 230); /* Soft gray for background */
    --color-background-secondary: rgb(240, 240, 240); /* Light gray for secondary backgrounds */
    --color-hover: #999792; /* Gray for hover states */
    --color-secondary-button: #000000; /* Black for secondary buttons */

    /* Typography */
    --font-family-primary: 'Montserrat', sans-serif;
    --font-weight-medium: 600;
    --font-weight-bold: 700;

    /* Font Sizes */
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-2xl: 2rem;

    /* Layout */
    --max-width: 1400px;
    --border-width: 1px;
    --border-style: solid;
    --border-color: rgb(204, 204, 204);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.25rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;

    /* Component Specific */
    --header-height: 75px;
    --footer-height: 35px;
  }

  /* Dark Theme */
  .dark {
    /* Color Palette */
    --color-primary: #FE650D; /* Keep orange for primary accents */
    --color-text: #ffffff; /* White for all text */
    --color-border: #333333; /* Dark gray for borders */
    --color-background: #1a1a1a; /* Dark background */
    --color-background-soft: #2a2a2a; /* Darker gray for soft backgrounds */
    --color-background-secondary: #333333; /* Dark gray for secondary backgrounds */
    --color-hover: #404040; /* Darker gray for hover states */
    --color-secondary-button: #ffffff; /* White for secondary buttons */
    --border-color: #333333;
  }
`;

// Helper function to inject theme into styled-components
export const injectGlobalTheme = () => css`
  ${theme}

  /* Global Styles */
  * {
    box-sizing: border-box;
    user-select: none;
    scrollbar-width: none; /* Firefox - hide all scrollbars */
    -ms-overflow-style: none; /* IE and Edge - hide all scrollbars */
  }

  *::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera - hide all scrollbars */
  }

  body {
    font-family: var(--font-family-primary);
    margin: 0;
    padding: 0;
    background-color: var(--color-background);
    color: var(--color-text);
    overflow-y: scroll; /* Allow scrolling */
  }

  html {
    overflow-y: scroll; /* Allow scrolling */
  }

  input,
  textarea {
    user-select: text;
  }

  button,
  select,
  a,
  [role="button"],
  [tabindex]:not([tabindex="-1"]),
  [data-scrollable],
  [role="menu"],
  [role="menuitem"],
  .profile-dropdown,
  .profile-dropdown * {
    pointer-events: auto;
  }

  button {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
`;
