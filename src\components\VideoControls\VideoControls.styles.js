import styled from 'styled-components';

export const ControlsContainer = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 10;

  &.show {
    opacity: 1;
    pointer-events: all;
  }
`;

export const ControlsOverlay = styled.div`
  display: flex;
  flex-direction: column;
  padding: 16px 0 12px 0;
  gap: 8px;
`;

export const ProgressBar = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 12px;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
`;

export const ProgressTrack = styled.div`
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
`;

export const ProgressFill = styled.div`
  height: 100%;
  background-color: var(--color-primary, #ff6b35);
  border-radius: 2px;
  transition: width 0.1s ease;
`;

export const PlayPauseButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }
`;

export const TimeDisplay = styled.span`
  color: white;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
`;

export const VolumeControl = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;

  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;

    &::-webkit-slider-track {
      background: rgba(255, 255, 255, 0.3);
      height: 4px;
      border-radius: 2px;
    }

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      background: var(--color-primary, #ff6b35);
      height: 12px;
      width: 12px;
      border-radius: 50%;
      cursor: pointer;
    }

    &::-moz-range-track {
      background: rgba(255, 255, 255, 0.3);
      height: 4px;
      border-radius: 2px;
      border: none;
    }

    &::-moz-range-thumb {
      background: var(--color-primary, #ff6b35);
      height: 12px;
      width: 12px;
      border-radius: 50%;
      cursor: pointer;
      border: none;
    }
  }
`;

export const FullscreenButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }
`;
