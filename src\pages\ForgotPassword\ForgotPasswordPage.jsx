import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import logo from '../../assets/zonacero-logo-01.svg';
import {
  PageWrapper,
  LoginHeader,
  LoginContainer,
  LoginCard,
  Logo,
  Form,
  InputGroup,
  Label,
  Input,
  Button,
  LinksContainer,
  Link,
  ErrorMessage,
  BottomLegend,
  Title,
} from '../Login/LoginPage.styles';

const ForgotPasswordPage = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!email.trim()) return;
    setMessage('Si el correo existe, te enviaremos instrucciones para restablecer tu contraseña.');
    setTimeout(() => navigate('/login'), 1500);
  };

  return (
    <PageWrapper>
      <LoginHeader>
        <Logo src={logo} alt="Zonacero Logo" />
      </LoginHeader>
      <LoginContainer>
        <LoginCard>
          <Title>Recuperar contraseña</Title>
          <ErrorMessage isVisible={!!message}>{message}</ErrorMessage>

          <Form onSubmit={handleSubmit}>
            <InputGroup>
              <Label htmlFor="email">Correo electrónico</Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                autoComplete="email"
                required
              />
            </InputGroup>
            <Button type="submit">Enviar instrucciones</Button>
          </Form>

          <LinksContainer>
            <Link href="#" onClick={(e) => { e.preventDefault(); navigate('/login'); }}>
              Volver al inicio de sesión
            </Link>
          </LinksContainer>
        </LoginCard>
      </LoginContainer>
      <BottomLegend>Powered by Aletheia Solutions</BottomLegend>
    </PageWrapper>
  );
};

export default ForgotPasswordPage;


