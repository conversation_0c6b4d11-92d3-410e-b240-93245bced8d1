# ZonaCero Backend API

Express.js API server for authentication, user management, and course access control.

**For full setup instructions, see the main [README.md](../README.md)**

## Quick Reference

### Environment Variables

Create `backend/.env`:

```env
PORT=4000
NODE_ENV=development
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/zonacero
JWT_SECRET=your-secret-key-change-in-production
COOKIE_NAME=token
CORS_ORIGIN=http://localhost:3000
```

Optional admin bootstrap:
```env
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=changeme
```

### Database Setup

```bash
# From project root
npm run run-schema      # Create base tables
npm run run-migration   # Run migrations
```

### Start Server

```bash
# From project root
npm run server

# Or directly
node backend/server.js
```

Server runs on `http://localhost:4000`

## API Endpoints

### Public
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login
- `GET /api/health` - Health check

### Authenticated (User)
- `GET /api/auth/me` - Current user info
- `POST /api/auth/logout` - Logout
- `GET /api/my/courses` - User's courses

### Admin Only
- `GET /api/admin/users` - List all users
- `GET /api/admin/courses` - List all courses
- `POST /api/admin/courses` - Create course `{ slug, title, description? }`
- `DELETE /api/admin/courses/:id` - Delete course
- `POST /api/admin/users/:userId/courses/:courseId` - Grant access
- `DELETE /api/admin/users/:userId/courses/:courseId` - Revoke access

## Database Schema

- **users**: User accounts with profile data and roles
- **courses**: Available courses with slug, title, description
- **user_courses**: Many-to-many relationship for course access

## Security

- JWT tokens stored in httpOnly cookies
- Password hashing with bcryptjs
- Role-based access control (user/admin)
- Admin endpoints protected by middleware
