import React from 'react';
import ReactDOM from 'react-dom/client';
import { createGlobalStyle } from 'styled-components';
import './index.css';
import App from './App';
import { injectGlobalTheme } from './theme';

// Inject global theme variables
const GlobalTheme = createGlobalStyle`
  ${injectGlobalTheme}
`;

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <GlobalTheme />
    <App />
  </React.StrictMode>
);