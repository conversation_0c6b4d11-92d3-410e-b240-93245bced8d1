import React, { useState, useEffect } from 'react';
import api from '../../services/api';

const YouTubeSettings = () => {
  const [credentials, setCredentials] = useState({
    youtubeApiKey: '',
    youtubeClientId: '',
    youtubeClientSecret: '',
    youtubeChannelId: '',
    hasRefreshToken: false,
    hasAccessToken: false,
    youtubeTokenExpiry: null
  });
  const [playlists, setPlaylists] = useState([]);
  const [channelInfo, setChannelInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeSection, setActiveSection] = useState('credentials');

  useEffect(() => {
    loadCredentials();
  }, []);

  const loadCredentials = async () => {
    try {
      const response = await api.get('/api/admin/youtube/credentials');
      setCredentials(prev => ({
        ...prev,
        ...response.data
      }));
    } catch (err) {
      console.error('Error loading credentials:', err);
    }
  };

  const handleCredentialChange = (field, value) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveCredentials = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await api.put('/api/admin/youtube/credentials', {
        youtubeApiKey: credentials.youtubeApiKey,
        youtubeClientId: credentials.youtubeClientId,
        youtubeClientSecret: credentials.youtubeClientSecret,
        youtubeRefreshToken: credentials.youtubeRefreshToken
      });
      setSuccess('Credenciales guardadas exitosamente');
      await loadCredentials(); // Reload to get updated status
    } catch (err) {
      console.error('Error saving credentials:', err);
      setError('Error al guardar las credenciales');
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.get('/api/admin/youtube/channel');
      setChannelInfo(response.data);
      setSuccess('Conexión exitosa con YouTube');
    } catch (err) {
      console.error('Error testing connection:', err);
      setError('Error al conectar con YouTube. Verifica tus credenciales.');
    } finally {
      setLoading(false);
    }
  };

  const loadPlaylists = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.get('/api/admin/youtube/playlists');
      setPlaylists(response.data);
      setSuccess(`Se encontraron ${response.data.length} playlists`);
    } catch (err) {
      console.error('Error loading playlists:', err);
      setError('Error al cargar las playlists');
    } finally {
      setLoading(false);
    }
  };

  const syncPlaylist = async (playlist) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await api.post('/api/admin/youtube/sync-playlist', {
        playlistId: playlist.id,
        title: playlist.snippet.title,
        description: playlist.snippet.description
      });
      setSuccess(`Playlist "${playlist.snippet.title}" sincronizada como curso`);
      // Reload playlists to show sync status
      await loadPlaylists();
    } catch (err) {
      console.error('Error syncing playlist:', err);
      setError('Error al sincronizar la playlist');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '1rem' }}>
      <h3>Configuración de YouTube</h3>

      {/* Tab Navigation */}
      <div style={{ display: 'flex', gap: '1rem', marginBottom: '2rem', borderBottom: '1px solid var(--color-border)' }}>
        <button
          onClick={() => setActiveSection('credentials')}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: activeSection === 'credentials' ? 'var(--color-primary)' : 'transparent',
            color: activeSection === 'credentials' ? 'white' : 'var(--color-text)',
            border: 'none',
            borderRadius: '4px 4px 0 0',
            cursor: 'pointer'
          }}
        >
          Credenciales API
        </button>
        <button
          onClick={() => setActiveSection('playlists')}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: activeSection === 'playlists' ? 'var(--color-primary)' : 'transparent',
            color: activeSection === 'playlists' ? 'white' : 'var(--color-text)',
            border: 'none',
            borderRadius: '4px 4px 0 0',
            cursor: 'pointer'
          }}
        >
          Sincronizar Playlists
        </button>
      </div>

      {/* Status Messages */}
      {error && (
        <div style={{
          padding: '1rem',
          backgroundColor: '#ff6b6b',
          color: 'white',
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      {success && (
        <div style={{
          padding: '1rem',
          backgroundColor: '#51cf66',
          color: 'white',
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {success}
        </div>
      )}

      {/* Credentials Section */}
      {activeSection === 'credentials' && (
        <div>
          <h4>Configuración de API de YouTube</h4>
          <p style={{ marginBottom: '1rem', fontSize: '0.9rem', color: 'var(--color-text-secondary)' }}>
            Para acceder a tus playlists privadas y videos no listados, configura tus credenciales de YouTube API.
          </p>

          <form onSubmit={saveCredentials}>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                YouTube API Key *
              </label>
              <input
                type="password"
                value={credentials.youtubeApiKey || ''}
                onChange={(e) => handleCredentialChange('youtubeApiKey', e.target.value)}
                placeholder="Ingresa tu YouTube API Key"
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid var(--color-border)',
                  borderRadius: '4px',
                  fontSize: '1rem'
                }}
                required
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                Client ID (para videos privados)
              </label>
              <input
                type="password"
                value={credentials.youtubeClientId || ''}
                onChange={(e) => handleCredentialChange('youtubeClientId', e.target.value)}
                placeholder="Ingresa tu OAuth Client ID"
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid var(--color-border)',
                  borderRadius: '4px',
                  fontSize: '1rem'
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                Client Secret (para videos privados)
              </label>
              <input
                type="password"
                value={credentials.youtubeClientSecret || ''}
                onChange={(e) => handleCredentialChange('youtubeClientSecret', e.target.value)}
                placeholder="Ingresa tu OAuth Client Secret"
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid var(--color-border)',
                  borderRadius: '4px',
                  fontSize: '1rem'
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                Refresh Token (para videos privados)
              </label>
              <input
                type="password"
                value={credentials.youtubeRefreshToken || ''}
                onChange={(e) => handleCredentialChange('youtubeRefreshToken', e.target.value)}
                placeholder="Ingresa tu OAuth Refresh Token"
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid var(--color-border)',
                  borderRadius: '4px',
                  fontSize: '1rem'
                }}
              />
            </div>

            <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
              <button
                type="submit"
                disabled={loading}
                style={{
                  padding: '0.75rem 1.5rem',
                  backgroundColor: 'var(--color-primary)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  opacity: loading ? 0.6 : 1
                }}
              >
                {loading ? 'Guardando...' : 'Guardar Credenciales'}
              </button>

              <button
                type="button"
                onClick={testConnection}
                disabled={loading || !credentials.youtubeApiKey}
                style={{
                  padding: '0.75rem 1.5rem',
                  backgroundColor: 'var(--color-secondary)',
                  color: 'var(--color-text)',
                  border: '1px solid var(--color-border)',
                  borderRadius: '4px',
                  cursor: (loading || !credentials.youtubeApiKey) ? 'not-allowed' : 'pointer',
                  opacity: (loading || !credentials.youtubeApiKey) ? 0.6 : 1
                }}
              >
                {loading ? 'Probando...' : 'Probar Conexión'}
              </button>
            </div>
          </form>

          {channelInfo && (
            <div style={{
              marginTop: '2rem',
              padding: '1rem',
              backgroundColor: 'var(--color-background-secondary)',
              borderRadius: '4px'
            }}>
              <h5>Información del Canal</h5>
              <p><strong>Nombre:</strong> {channelInfo.snippet.title}</p>
              <p><strong>ID:</strong> {channelInfo.id}</p>
              <p><strong>Suscriptores:</strong> {channelInfo.statistics.subscriberCount}</p>
              <p><strong>Videos:</strong> {channelInfo.statistics.videoCount}</p>
            </div>
          )}
        </div>
      )}

      {/* Playlists Section */}
      {activeSection === 'playlists' && (
        <div>
          <h4>Sincronización de Playlists</h4>
          <p style={{ marginBottom: '1rem', fontSize: '0.9rem', color: 'var(--color-text-secondary)' }}>
            Importa tus playlists de YouTube como cursos en la plataforma.
          </p>

          <div style={{ marginBottom: '2rem' }}>
            <button
              onClick={loadPlaylists}
              disabled={loading || !credentials.youtubeApiKey}
              style={{
                padding: '0.75rem 1.5rem',
                backgroundColor: 'var(--color-primary)',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: (loading || !credentials.youtubeApiKey) ? 'not-allowed' : 'pointer',
                opacity: (loading || !credentials.youtubeApiKey) ? 0.6 : 1
              }}
            >
              {loading ? 'Cargando...' : 'Cargar Playlists'}
            </button>
          </div>

          {playlists.length > 0 && (
            <div>
              <h5>Playlists Disponibles ({playlists.length})</h5>
              <div style={{ display: 'grid', gap: '1rem' }}>
                {playlists.map((playlist) => (
                  <div
                    key={playlist.id}
                    style={{
                      padding: '1rem',
                      border: '1px solid var(--color-border)',
                      borderRadius: '4px',
                      backgroundColor: 'var(--color-background-secondary)'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                      <div style={{ flex: 1 }}>
                        <h6 style={{ margin: '0 0 0.5rem 0' }}>{playlist.snippet.title}</h6>
                        <p style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem', color: 'var(--color-text-secondary)' }}>
                          {playlist.snippet.description || 'Sin descripción'}
                        </p>
                        <p style={{ margin: '0', fontSize: '0.8rem', color: 'var(--color-text-secondary)' }}>
                          {playlist.contentDetails ? `${playlist.contentDetails.itemCount} videos` : 'Videos: N/A'} •
                          Privacidad: {playlist.status.privacyStatus}
                        </p>
                      </div>
                      <button
                        onClick={() => syncPlaylist(playlist)}
                        disabled={loading}
                        style={{
                          padding: '0.5rem 1rem',
                          backgroundColor: '#51cf66',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: loading ? 'not-allowed' : 'pointer',
                          opacity: loading ? 0.6 : 1
                        }}
                      >
                        Sincronizar
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default YouTubeSettings;
