import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  ControlsContainer,
  ControlsOverlay,
  PlayPauseButton,
  ProgressBar,
  ProgressTrack,
  ProgressFill,
  TimeDisplay,
  VolumeControl,
  FullscreenButton
} from './VideoControls.styles';

const VideoControls = ({
  player,
  isPlaying,
  onPlayPause,
  currentTime = 0,
  duration = 0,
  onSeek,
  volume = 100,
  onVolumeChange,
  onFullscreen,
  showControls = true,
  onControlsHover,
  onControlsLeave
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localCurrentTime, setLocalCurrentTime] = useState(currentTime);
  const progressBarRef = useRef(null);

  useEffect(() => {
    if (!isDragging) {
      setLocalCurrentTime(currentTime);
    }
  }, [currentTime, isDragging]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = useCallback((e) => {
    e.preventDefault();
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;
    onSeek(newTime);
  }, [duration, onSeek]);

  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsDragging(true);
    handleProgressClick(e);
  }, [handleProgressClick]);

  useEffect(() => {
    if (!isDragging) return;

    const handleProgressDrag = (e) => {
      if (!progressBarRef.current) return;

      e.preventDefault();
      const rect = progressBarRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, clickX / rect.width));
      const newTime = percentage * duration;
      setLocalCurrentTime(newTime);
    };

    const handleMouseUp = () => {
      onSeek(localCurrentTime);
      setIsDragging(false);
    };

    document.addEventListener('mousemove', handleProgressDrag);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleProgressDrag);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, duration, localCurrentTime, onSeek]);

  return (
    <ControlsContainer
      className={showControls ? 'show' : ''}
      onMouseEnter={onControlsHover}
      onMouseLeave={onControlsLeave}
    >
      <ControlsOverlay>
        {/* Progress Bar */}
        <ProgressBar
          ref={progressBarRef}
          onClick={handleProgressClick}
          onMouseDown={handleMouseDown}
        >
          <ProgressTrack>
            <ProgressFill 
              style={{ 
                width: `${duration > 0 ? (localCurrentTime / duration) * 100 : 0}%` 
              }} 
            />
          </ProgressTrack>
        </ProgressBar>

        {/* Controls Row */}
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0 12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Play/Pause Button */}
            <PlayPauseButton onClick={onPlayPause}>
              {isPlaying ? (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </PlayPauseButton>

            {/* Time Display */}
            <TimeDisplay>
              {formatTime(localCurrentTime)} / {formatTime(duration)}
            </TimeDisplay>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Volume Control */}
            <VolumeControl>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              </svg>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => onVolumeChange(parseInt(e.target.value))}
                style={{
                  width: '60px',
                  height: '4px',
                  background: 'rgba(255, 255, 255, 0.3)',
                  outline: 'none',
                  borderRadius: '2px',
                  cursor: 'pointer'
                }}
              />
            </VolumeControl>

            {/* Fullscreen Button */}
            <FullscreenButton onClick={onFullscreen}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
              </svg>
            </FullscreenButton>
          </div>
        </div>
      </ControlsOverlay>
    </ControlsContainer>
  );
};

export default VideoControls;
