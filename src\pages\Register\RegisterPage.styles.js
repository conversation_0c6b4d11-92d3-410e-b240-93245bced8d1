import styled from 'styled-components';

// Register layout container - side by side
export const RegisterContainer = styled.div`
  display: flex;
  gap: var(--spacing-xl);
  width: 100%;
  align-items: flex-start;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
`;

// Left side - step titles
export const StepTitles = styled.div`
  flex: 0 0 150px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  @media (max-width: 768px) {
    flex: 1;
    width: 100%;
  }
`;

export const StepTitle = styled.div`
  font-size: var(--font-size-sm);
  font-weight: ${props => props.$active ? 'var(--font-weight-bold)' : 'var(--font-weight-medium)'};
  color: ${props => props.$active ? 'var(--color-primary)' : 'var(--color-hover)'};
  padding: var(--spacing-xs) var(--spacing-sm);
  border-left: 3px solid ${props => props.$active ? 'var(--color-primary)' : 'transparent'};
  transition: all 0.3s ease;
  cursor: default;
  line-height: 1.4;

  @media (max-width: 768px) {
    border-left: none;
    border-bottom: 2px solid ${props => props.$active ? 'var(--color-primary)' : 'transparent'};
  }
`;

// Right side - form content
export const FormContent = styled.div`
  flex: 1;
  min-width: 0;
`;

// Slider container with overflow hidden
export const SliderContainer = styled.div`
  overflow: hidden;
  width: 100%;
  position: relative;
`;

// Slides wrapper that translates horizontally
export const Slides = styled.div`
  display: flex;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-${props => (props.$step / props.$steps) * 100}%);
  width: ${props => props.$steps * 100}%;
`;

// Individual slide
export const Slide = styled.div`
  flex: 0 0 ${props => (100 / props.$steps)}%;
  width: ${props => (100 / props.$steps)}%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: 0 2px; /* Small padding to prevent border clipping */
`;

// Step actions (navigation buttons)
export const StepActions = styled.div`
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);

  button {
    flex: 1;
  }
`;

// Secondary button (back button)
export const SecondaryButton = styled.button`
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  background-color: transparent;
  border: var(--border-width) var(--border-style) var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease;

  &:hover {
    background-color: var(--color-background-soft);
    border-color: var(--color-text);
  }

  &:active {
    transform: scale(0.98);
  }
`;

// Select dropdown
export const Select = styled.select`
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  color: var(--color-text);
  background-color: var(--color-background);
  border: var(--border-width) var(--border-style) var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(254, 101, 13, 0.1);
  }

  option {
    background-color: var(--color-background);
    color: var(--color-text);
  }
`;

// Checkbox label
export const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--color-text);
  cursor: pointer;
  padding: var(--spacing-xs) 0;

  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--color-primary);
  }

  span {
    flex: 1;
    line-height: 1.4;
  }

  &:hover {
    color: var(--color-primary);
  }
`;

