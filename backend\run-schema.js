const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const db = require('./db');

async function runSchema() {
	console.log('Running database schema...');
	console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set (hidden)' : 'NOT SET');
	
	if (!process.env.DATABASE_URL) {
		console.error('\nERROR: DATABASE_URL is not set in backend/.env file');
		console.error('Create a backend/.env file with your database INFORmation string:');
		console.error('DATABASE_URL=postgresql://username:password@localhost:5432/dbname');
		process.exit(1);
	}
	
	try {
		// Read the schema file
		const schemaSQL = fs.readFileSync(path.join(__dirname, 'schema.sql'), 'utf8');
		
		// Execute the schema
		await db.query(schemaSQL);
		
		console.log('✓ Schema executed successfully!');
		console.log('Tables created: users');
		
		process.exit(0);
	} catch (err) {
		console.error('✗ Schema execution failed:');
		console.error('Error:', err.message);
		console.error('Code:', err.code);
		
		process.exit(1);
	}
}

runSchema();

