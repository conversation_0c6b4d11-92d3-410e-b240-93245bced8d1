import styled from 'styled-components';

export const HeaderContainer = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-2xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-background);
  z-index: 1000;

  @media (max-width: 768px) {
    padding: 0 var(--spacing-lg);
  }
`;

export const Logo = styled.img`
  height: 75px;
  width: auto;
  cursor: pointer;
  transition: transform 0.2s ease;
  user-select: none;

  &:hover {
    transform: scale(1.05);
  }
`;

export const CourseTitle = styled.h1`
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0;
  text-align: center;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;

  @media (max-width: 768px) {
    font-size: 0.875rem;
    text-align: center;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
  }
`;

export const SearchBar = styled.div`
  width: 50%;
  height: 100%;
  align-items: center;
  display: flex;
  gap: var(--spacing-sm);
  justify-content: space-between;

  @media (max-width: 768px) {
    display: none;
  }
`;

export const SearchInput = styled.input`
  flex: 1;
  min-height: 50px;
  padding: 0 var(--spacing-xl);
  border: var(--border-width) var(--border-style) var(--border-color);
  background:transparent;
  border-radius: 12px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-text);

  &:focus {
    outline: 2px solid var(--color-primary);
    border-color: var(--color-primary);
  }
`;

export const SearchButton = styled.button`
  height: 50%;
  min-width: 100px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: 12px;
  font-weight: var(--font-weight-bold);
  background-color: transparent;
  transition: all 0.3s ease;
  color: var(--color-text);
  cursor: pointer;
  user-select: none;

  &:hover {
    color: var(--color-primary);
    background-color: var(--color-background-soft);
  }
`;

export const StudentName = styled.p`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin: 0;
  color: var(--color-text);
  user-select: none;

  @media (max-width: 768px) {
    display: none;
  }
`;

export const StudentNameSpan = styled.span`
  color: var(--color-primary);
  user-select: none;
`;

export const UserNav = styled.div`
  height: 100%;
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;

  @media (max-width: 768px) {
    display: none;
  }
`;

export const NavItem = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 40%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color:var(--color-text);
  pointer-events: auto;
  position: relative;
  user-select: none;

  i {
    font-size: var(--font-size-xl);
  }

  &:hover {
    background-color: var(--color-primary);
    color:var(--color-background);
  }
`;

export const Tooltip = styled.div`
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-text);
  color: var(--color-background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1001;
  margin-top: 8px;
  user-select: none;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--color-text);
  }
`;

export const NavItemWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;

  &:hover ${Tooltip} {
    opacity: 1;
    visibility: visible;
  }
`;

export const BurgerMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-md);
  min-width: 44px;
  min-height: 44px;
  color: var(--color-text);
  font-size: var(--font-size-xl);
  transition: color 0.2s ease;
  user-select: none;
  border-radius: 4px;

  &:hover {
    color: var(--color-primary);
  }

  &:active {
    background-color: var(--color-hover);
  }

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

export const MobileMenuOverlay = styled.div`
  display: none;

  @media (max-width: 768px) {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: ${props => props.isOpen ? 'block' : 'none'};
    pointer-events: auto;
  }
`;

export const MobileMenu = styled.div`
  display: none;

  @media (max-width: 768px) {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    background-color: var(--color-background);
    border-top: 1px solid var(--color-border);
    padding: var(--spacing-lg);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 999;
    display: ${props => props.isOpen ? 'block' : 'none'};
    pointer-events: auto;
    max-height: calc(100vh - var(--header-height));
    overflow-y: auto;
  }
`;

export const MobileMenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--color-text);
  min-height: 44px;
  pointer-events: auto;

  &:hover {
    background-color: var(--color-primary);
    color: var(--color-background);
  }

  &:active {
    background-color: var(--color-hover);
  }

  i {
    margin-right: var(--spacing-md);
    font-size: var(--font-size-lg);
  }
`;

export const MobileSearchBar = styled.div`
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
`;

export const MobileSearchInput = styled.input`
  flex: 1;
  padding: var(--spacing-md);
  border: var(--border-width) var(--border-style) var(--border-color);
  background: transparent;
  border-radius: 4px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-text);

  &:focus {
    outline: 2px solid var(--color-primary);
    border-color: var(--color-primary);
  }
`;

export const MobileSearchButton = styled.button`
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-primary);
  color: var(--color-background);
  border: var(--border-width) var(--border-style) var(--color-primary);
  border-radius: 4px;
  font-weight: var(--font-weight-bold);
  transition: background-color 0.2s ease;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--color-hover);
    border-color: var(--color-hover);
  }
`;

export const MobileStudentName = styled.div`
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-lg);

  span {
    color: var(--color-primary);
  }
`;

export const MobileThemeToggle = styled.div`
  margin-left: auto;
  width: 44px;
  height: 24px;
  background-color: ${props => props.checked ? 'var(--color-primary)' : '#ccc'};
  border-radius: 24px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
  user-select: none;

  &::before {
    content: '';
    position: absolute;
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    transform: ${props => props.checked ? 'translateX(20px)' : 'translateX(0)'};
  }
`;
