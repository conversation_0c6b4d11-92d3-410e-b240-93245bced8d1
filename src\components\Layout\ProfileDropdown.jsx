import React from 'react';
import { useTheme } from './ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import {
  DropdownContainer,
  DropdownMenu,
  DropdownItem,
  ThemeToggleContainer,
  ThemeToggleLabel,
  ThemeToggleSwitch,
  ThemeToggleSlider,
  LogoutItem
} from './ProfileDropdown.styles';

const ProfileDropdown = ({ isOpen, onMouseEnter, onMouseLeave, mobile = false }) => {
  const { isDarkTheme, toggleTheme } = useTheme();
  const { logout } = useAuth();

  // Handle theme toggle
  const handleThemeToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    toggleTheme();
    console.log('Theme toggled:', !isDarkTheme);
  };

  // Handle logout
  const handleLogout = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      await logout();
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <DropdownContainer
      className="profile-dropdown"
      $mobile={mobile}
      onMouseEnter={!mobile ? onMouseEnter : undefined}
      onMouseLeave={!mobile ? onMouseLeave : undefined}
    >
      <DropdownMenu $mobile={mobile}>
        <DropdownItem $mobile={mobile} onClick={handleThemeToggle}>
          <ThemeToggleContainer>
            <ThemeToggleLabel>Tema oscuro</ThemeToggleLabel>
            <ThemeToggleSwitch
              type="checkbox"
              checked={isDarkTheme}
              onChange={handleThemeToggle}
            />
            <ThemeToggleSlider checked={isDarkTheme} />
          </ThemeToggleContainer>
        </DropdownItem>
        <LogoutItem $mobile={mobile} onClick={handleLogout}>
          Cerrar sesión
        </LogoutItem>
      </DropdownMenu>
    </DropdownContainer>
  );
};

export default ProfileDropdown;
