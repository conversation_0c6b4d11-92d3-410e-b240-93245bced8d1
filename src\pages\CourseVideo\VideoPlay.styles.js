import styled from 'styled-components';

export const VideoPlayContainer = styled.div`
  height: 100dvh;
  max-width: var(--max-width);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  overflow: hidden;

  @media (max-width: 768px) {
    height: auto;
    min-height: 100dvh;
    overflow: visible;
  }
`;

export const VideoPlayContent = styled.main`
  flex: 1;
  height: calc(100dvh - var(--header-height));
  margin-top: var(--header-height);
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  overflow: hidden;

  @media (max-width: 768px) {
    height: auto;
    overflow: visible;
  }
`;

export const MainContent = styled.div`
  width: 100%;
  height: 100%;
  max-width: ${props => props.$isFullscreen ? 'none' : 'var(--max-width)'};
  margin: ${props => props.$isFullscreen ? '0' : '0 auto'};
  display: ${props => props.$isFullscreen ? 'block' : 'grid'};
  grid-template-columns: ${props => props.$isFullscreen ? 'none' : '1fr 402px'};
  gap: ${props => props.$isFullscreen ? '0' : 'var(--spacing-xl)'};
  padding: ${props => props.$isFullscreen ? '0' : 'var(--spacing-xl)'};
  align-items: start;
  overflow: hidden;

  @media (max-width: 1200px) {
    grid-template-columns: ${props => props.$isFullscreen ? 'none' : '1fr'};
  }

  @media (max-width: 768px) {
    height: auto;
    overflow: visible;
  }
`;

export const PrimaryVideoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

export const LeftColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  height: 100%;
`;

export const RightColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  height: 100%;
  overflow: hidden;
`;

export const VideoPlayer = styled.div`
  width: 100%;
  height: ${props => props.$isFullscreen ? '100vh' : 'auto'};
  aspect-ratio: ${props => props.$isFullscreen ? 'none' : '16 / 10'};
  background-color: var(--color-text);
  border-radius: ${props => props.$isFullscreen ? '0' : '12px'};
  overflow: hidden;
  position: ${props => props.$isFullscreen ? 'fixed' : 'relative'};
  top: ${props => props.$isFullscreen ? '0' : 'auto'};
  left: ${props => props.$isFullscreen ? '0' : 'auto'};
  right: ${props => props.$isFullscreen ? '0' : 'auto'};
  bottom: ${props => props.$isFullscreen ? '0' : 'auto'};
  z-index: ${props => props.$isFullscreen ? '9999' : 'auto'};
  pointer-events: none;

  /* Style the YouTube iframe created by the API */
  iframe {
    pointer-events: none !important;
    border: none;
    width: 300% !important;
    height: 100% !important;
    margin-left: -100% !important;
  }

  #youtube-player {
    overflow: hidden;
    width: 100%;
    height: 100%;
  }

  #youtube-player iframe {
    pointer-events: none !important;
    width: 300% !important;
    height: 100% !important;
    margin-left: -100% !important;
  }
`;

export const VideoPlayerIframe = styled.iframe`
  width: 300%;
  height: 100%;
  margin-left: -100%;
  border: none;
  pointer-events: none;
`;

export const SuggestionOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary, #FE650D) 0%, #d54d0a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  z-index: 10;
  pointer-events: none;

  i {
    font-size: 1.5em;
    color: inherit;
  }

  /* Enable pointer events for interactive elements */
  button {
    pointer-events: all;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export const MainVideo = styled.video`
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  display: block;
`;

export const VideoInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

export const VideoTitle = styled.h1`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  color: var(--color-text);
  margin: 0;
  user-select: none;
`;

export const VideoMetadata = styled.div`
  display: flex;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-hover);
  user-select: none;

  span::after {
    content: " • ";
    margin-left: var(--spacing-sm);
  }

  span:last-child::after {
    content: "";
  }
`;

export const ChannelInfo = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
`;

export const ChannelAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
`;

export const ChannelDetails = styled.div`
  flex: 1;
`;

export const ChannelName = styled.h3`
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--color-text);
  user-select: none;
`;

export const Subscribers = styled.p`
  font-size: 12px;
  color: var(--color-hover);
  margin: 0;
  user-select: none;
`;



export const VideoPlaySidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex: 1;
  min-height: 0; /* Allow flex item to shrink below content size */
  overflow-y: auto;
  padding-right: var(--spacing-sm);
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  @media (max-width: 1200px) {
    flex: none;
    max-height: 400px; /* Limit height on tablet/mobile when sidebar is below video */
  }

  @media (max-width: 768px) {
    flex: none;
    max-height: none;
  }
`;

export const SidebarItem = styled.div`
  display: flex;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: background-color 0.2s;
  padding: var(--spacing-xs);
  border-radius: 8px;
  user-select: none;

  &:hover {
    background-color: var(--color-background-soft);
  }
`;

export const ThumbnailContainer = styled.div`
  position: relative;
  width: 120px;
  height: 90px;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 4px;
`;

export const SidebarVideo = styled.video`
  width: 100%;
  height: 94px;
  aspect-ratio: 16/9;
  background-color: var(--color-background-soft);
  border-radius: 8px;
  object-fit: cover;
  display: block;
`;

export const VideoDuration = styled.span`
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  user-select: none;
`;

export const VideoDetails = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  overflow: hidden;
`;

export const VideoTitleSidebar = styled.h3`
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  color: var(--color-text);
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  user-select: none;
`;

export const ChannelNameSidebar = styled.p`
  font-size: 12px;
  color: var(--color-hover);
  margin: 0;
  user-select: none;
`;

export const VideoStats = styled.div`
  font-size: 12px;
  color: var(--color-hover);
  display: flex;
  gap: var(--spacing-xs);
  user-select: none;
`;
