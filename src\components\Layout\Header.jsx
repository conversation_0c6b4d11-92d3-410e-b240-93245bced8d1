import React, { useState } from 'react';
import 'boxicons/css/boxicons.min.css';
import logo from '../../assets/zonacero-logo-01.svg';
import ProfileDropdown from './ProfileDropdown';
import { useTheme } from './ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import {
  HeaderContainer,
  Logo,
  SearchBar,
  SearchInput,
  SearchButton,
  StudentName,
  StudentNameSpan,
  UserNav,
  NavItem,
  NavItemWrapper,
  BurgerMenuButton,
  MobileMenuOverlay,
  MobileMenu,
  MobileMenuItem,
  MobileSearchBar,
  MobileSearchInput,
  MobileSearchButton,
  MobileStudentName,
  MobileThemeToggle
} from './Header.styles';

const Header = ({ searchQuery, setSearchQuery, handleSearch }) => {
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const closeTimeoutRef = React.useRef(null);
  const { isDarkTheme, toggleTheme } = useTheme();
  const { logout, user } = useAuth();

  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    setIsProfileDropdownOpen(true);
  };

  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setIsProfileDropdownOpen(false);
    }, 200);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleMobileSearch = () => {
    handleSearch(searchQuery);
    closeMobileMenu();
  };

  const handleThemeToggle = () => {
    toggleTheme();
  };

  const handleLogout = async () => {
    try {
      await logout();
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };


  return (
    <HeaderContainer>
      <a href="https://www.zonaceroescuela.com" target="_blank" rel="noopener noreferrer">
        <Logo src={logo} alt="Zonacero Logo" />
      </a>
      <SearchBar>
        <SearchInput
          type="text"
          placeholder="Buscar curso..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
        />
        <SearchButton onClick={() => handleSearch(searchQuery)}>Buscar</SearchButton>
      </SearchBar>
      <StudentName>Bienvenido, <StudentNameSpan>{user?.first_name || 'Usuario'}</StudentNameSpan></StudentName>
      <UserNav>
        <NavItemWrapper
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <NavItem>
            <i className='bx bx-user'></i>
          </NavItem>
          <ProfileDropdown
            isOpen={isProfileDropdownOpen}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          />
        </NavItemWrapper>
      </UserNav>
      <BurgerMenuButton onClick={toggleMobileMenu}>
        <i className={isMobileMenuOpen ? 'bx bx-x' : 'bx bx-menu'}></i>
      </BurgerMenuButton>
      <MobileMenuOverlay isOpen={isMobileMenuOpen} onClick={closeMobileMenu} />
      <MobileMenu isOpen={isMobileMenuOpen}>
        <MobileSearchBar>
          <MobileSearchInput
            type="text"
            placeholder="Buscar curso..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleMobileSearch()}
          />
          <MobileSearchButton onClick={handleMobileSearch}>Buscar</MobileSearchButton>
        </MobileSearchBar>
        <MobileStudentName>
          Bienvenido, <span>{user?.first_name || 'Usuario'}</span>
        </MobileStudentName>
        <MobileMenuItem onClick={handleThemeToggle}>
          <i className='bx bx-moon'></i>
          Tema oscuro
          <MobileThemeToggle checked={isDarkTheme} />
        </MobileMenuItem>
        <MobileMenuItem onClick={handleLogout}>
          <i className='bx bx-log-out'></i>
          Cerrar sesión
        </MobileMenuItem>
      </MobileMenu>
    </HeaderContainer>
  );
};

export default Header;
