import styled from 'styled-components';

// Page wrapper - uses 100dvh to prevent scroll
export const PageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100dvh;
  overflow: hidden;
  background-color: var(--color-background);
`;

// Header with logo - fixed height
export const LoginHeader = styled.header`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  flex-shrink: 0;
  background-color: var(--color-background);
`;

export const Logo = styled.img`
  height: 50px;
  width: auto;
`;

// Main container - takes remaining space and centers card
export const LoginContainer = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--color-background);
  overflow-y: auto;
`;

// Card container with fixed dimensions
export const LoginCard = styled.div`
  width: 100%;
  max-width: ${props => props.$wide ? '750px' : '420px'};
  background-color: var(--color-background);
  border: var(--border-width) var(--border-style) var(--border-color);
  border-radius: 8px;
  padding: ${props => props.$wide ? 'var(--spacing-lg) var(--spacing-2xl)' : 'var(--spacing-xl) var(--spacing-2xl)'};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  @media (max-width: 768px) {
    max-width: 420px;
    padding: var(--spacing-xl) var(--spacing-2xl);
  }
`;

// Optional title for pages like ForgotPassword
export const Title = styled.h1`
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--spacing-sm) 0;
  text-align: center;
`;

// Fixed height error/success message area - maintains space when empty
export const ErrorMessage = styled.div`
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
  color: ${props => props.isVisible ? 'var(--color-text)' : 'transparent'};
  background-color: ${props => props.isVisible ? 'var(--color-background-soft)' : 'transparent'};
  border: 1px solid ${props => props.isVisible ? 'var(--border-color)' : 'transparent'};
  transition: all 0.2s ease;
  pointer-events: ${props => props.isVisible ? 'auto' : 'none'};
  user-select: ${props => props.isVisible ? 'auto' : 'none'};
`;

// Form
export const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

// Input groups
export const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
`;

export const Label = styled.label`
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
`;

export const Input = styled.input`
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  color: var(--color-text);
  background-color: var(--color-background);
  border: var(--border-width) var(--border-style) var(--border-color);
  border-radius: 4px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(254, 101, 13, 0.1);
  }

  &::placeholder {
    color: var(--color-hover);
  }
`;

// Primary button
export const Button = styled.button`
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: #ffffff;
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;

  &:hover {
    background-color: #e55a0b;
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    background-color: var(--color-hover);
    cursor: not-allowed;
  }
`;

// Links container
export const LinksContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
`;

export const Link = styled.a`
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: var(--color-primary);
  }
`;

// Bottom legend - fixed height
export const BottomLegend = styled.div`
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  font-size: var(--font-size-sm);
  color: var(--color-hover);
  background-color: var(--color-background);
`;

